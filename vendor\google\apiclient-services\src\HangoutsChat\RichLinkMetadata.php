<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\HangoutsChat;

class RichLinkMetadata extends \Google\Model
{
  protected $chatSpaceLinkDataType = ChatSpaceLinkData::class;
  protected $chatSpaceLinkDataDataType = '';
  protected $driveLinkDataType = DriveLinkData::class;
  protected $driveLinkDataDataType = '';
  /**
   * @var string
   */
  public $richLinkType;
  /**
   * @var string
   */
  public $uri;

  /**
   * @param ChatSpaceLinkData
   */
  public function setChatSpaceLinkData(ChatSpaceLinkData $chatSpaceLinkData)
  {
    $this->chatSpaceLinkData = $chatSpaceLinkData;
  }
  /**
   * @return ChatSpaceLinkData
   */
  public function getChatSpaceLinkData()
  {
    return $this->chatSpaceLinkData;
  }
  /**
   * @param DriveLinkData
   */
  public function setDriveLinkData(DriveLinkData $driveLinkData)
  {
    $this->driveLinkData = $driveLinkData;
  }
  /**
   * @return DriveLinkData
   */
  public function getDriveLinkData()
  {
    return $this->driveLinkData;
  }
  /**
   * @param string
   */
  public function setRichLinkType($richLinkType)
  {
    $this->richLinkType = $richLinkType;
  }
  /**
   * @return string
   */
  public function getRichLinkType()
  {
    return $this->richLinkType;
  }
  /**
   * @param string
   */
  public function setUri($uri)
  {
    $this->uri = $uri;
  }
  /**
   * @return string
   */
  public function getUri()
  {
    return $this->uri;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(RichLinkMetadata::class, 'Google_Service_HangoutsChat_RichLinkMetadata');
