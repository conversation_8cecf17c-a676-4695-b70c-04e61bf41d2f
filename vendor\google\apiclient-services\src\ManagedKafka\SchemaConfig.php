<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\ManagedKafka;

class SchemaConfig extends \Google\Model
{
  /**
   * @var string
   */
  public $alias;
  /**
   * @var string
   */
  public $compatibility;
  /**
   * @var bool
   */
  public $normalize;

  /**
   * @param string
   */
  public function setAlias($alias)
  {
    $this->alias = $alias;
  }
  /**
   * @return string
   */
  public function getAlias()
  {
    return $this->alias;
  }
  /**
   * @param string
   */
  public function setCompatibility($compatibility)
  {
    $this->compatibility = $compatibility;
  }
  /**
   * @return string
   */
  public function getCompatibility()
  {
    return $this->compatibility;
  }
  /**
   * @param bool
   */
  public function setNormalize($normalize)
  {
    $this->normalize = $normalize;
  }
  /**
   * @return bool
   */
  public function getNormalize()
  {
    return $this->normalize;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(SchemaConfig::class, 'Google_Service_ManagedKafka_SchemaConfig');
