<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\HangoutsChat;

class Button extends \Google\Model
{
  protected $imageButtonType = ImageButton::class;
  protected $imageButtonDataType = '';
  protected $textButtonType = TextButton::class;
  protected $textButtonDataType = '';

  /**
   * @param ImageButton
   */
  public function setImageButton(ImageButton $imageButton)
  {
    $this->imageButton = $imageButton;
  }
  /**
   * @return ImageButton
   */
  public function getImageButton()
  {
    return $this->imageButton;
  }
  /**
   * @param TextButton
   */
  public function setTextButton(TextButton $textButton)
  {
    $this->textButton = $textButton;
  }
  /**
   * @return TextButton
   */
  public function getTextButton()
  {
    return $this->textButton;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Button::class, 'Google_Service_HangoutsChat_Button');
