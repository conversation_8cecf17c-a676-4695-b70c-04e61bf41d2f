<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\IAMCredentials;

class GenerateIdTokenRequest extends \Google\Collection
{
  protected $collection_key = 'delegates';
  /**
   * @var string
   */
  public $audience;
  /**
   * @var string[]
   */
  public $delegates;
  /**
   * @var bool
   */
  public $includeEmail;
  /**
   * @var bool
   */
  public $organizationNumberIncluded;

  /**
   * @param string
   */
  public function setAudience($audience)
  {
    $this->audience = $audience;
  }
  /**
   * @return string
   */
  public function getAudience()
  {
    return $this->audience;
  }
  /**
   * @param string[]
   */
  public function setDelegates($delegates)
  {
    $this->delegates = $delegates;
  }
  /**
   * @return string[]
   */
  public function getDelegates()
  {
    return $this->delegates;
  }
  /**
   * @param bool
   */
  public function setIncludeEmail($includeEmail)
  {
    $this->includeEmail = $includeEmail;
  }
  /**
   * @return bool
   */
  public function getIncludeEmail()
  {
    return $this->includeEmail;
  }
  /**
   * @param bool
   */
  public function setOrganizationNumberIncluded($organizationNumberIncluded)
  {
    $this->organizationNumberIncluded = $organizationNumberIncluded;
  }
  /**
   * @return bool
   */
  public function getOrganizationNumberIncluded()
  {
    return $this->organizationNumberIncluded;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GenerateIdTokenRequest::class, 'Google_Service_IAMCredentials_GenerateIdTokenRequest');
