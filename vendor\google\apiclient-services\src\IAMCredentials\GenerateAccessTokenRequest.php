<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\IAMCredentials;

class GenerateAccessTokenRequest extends \Google\Collection
{
  protected $collection_key = 'scope';
  /**
   * @var string[]
   */
  public $delegates;
  /**
   * @var string
   */
  public $lifetime;
  /**
   * @var string[]
   */
  public $scope;

  /**
   * @param string[]
   */
  public function setDelegates($delegates)
  {
    $this->delegates = $delegates;
  }
  /**
   * @return string[]
   */
  public function getDelegates()
  {
    return $this->delegates;
  }
  /**
   * @param string
   */
  public function setLifetime($lifetime)
  {
    $this->lifetime = $lifetime;
  }
  /**
   * @return string
   */
  public function getLifetime()
  {
    return $this->lifetime;
  }
  /**
   * @param string[]
   */
  public function setScope($scope)
  {
    $this->scope = $scope;
  }
  /**
   * @return string[]
   */
  public function getScope()
  {
    return $this->scope;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GenerateAccessTokenRequest::class, 'Google_Service_IAMCredentials_GenerateAccessTokenRequest');
