<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\HangoutsChat;

class EmojiReactionSummary extends \Google\Model
{
  protected $emojiType = Emoji::class;
  protected $emojiDataType = '';
  /**
   * @var int
   */
  public $reactionCount;

  /**
   * @param Emoji
   */
  public function setEmoji(Emoji $emoji)
  {
    $this->emoji = $emoji;
  }
  /**
   * @return Emoji
   */
  public function getEmoji()
  {
    return $this->emoji;
  }
  /**
   * @param int
   */
  public function setReactionCount($reactionCount)
  {
    $this->reactionCount = $reactionCount;
  }
  /**
   * @return int
   */
  public function getReactionCount()
  {
    return $this->reactionCount;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(EmojiReactionSummary::class, 'Google_Service_HangoutsChat_EmojiReactionSummary');
