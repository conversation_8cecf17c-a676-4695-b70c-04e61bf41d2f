<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Iam;

class GoogleIamV2PolicyRule extends \Google\Model
{
  protected $denyRuleType = GoogleIamV2DenyRule::class;
  protected $denyRuleDataType = '';
  /**
   * @var string
   */
  public $description;

  /**
   * @param GoogleIamV2DenyRule
   */
  public function setDenyRule(GoogleIamV2DenyRule $denyRule)
  {
    $this->denyRule = $denyRule;
  }
  /**
   * @return GoogleIamV2DenyRule
   */
  public function getDenyRule()
  {
    return $this->denyRule;
  }
  /**
   * @param string
   */
  public function setDescription($description)
  {
    $this->description = $description;
  }
  /**
   * @return string
   */
  public function getDescription()
  {
    return $this->description;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleIamV2PolicyRule::class, 'Google_Service_Iam_GoogleIamV2PolicyRule');
